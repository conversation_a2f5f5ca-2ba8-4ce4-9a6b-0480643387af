import { PayFast } from 'node-payfast'
import { C<PERSON>, OrdersController, PaymentsController } from '@paypal/paypal-server-sdk'
import { createHash, createHmac } from 'crypto'
import { prisma } from '@/lib/prisma'
import { securityService } from './security-service'

// Types
export interface PaymentConfig {
  payfast: {
    merchantId: string
    merchantKey: string
    passphrase: string
    sandbox: boolean
  }
  paypal: {
    clientId: string
    clientSecret: string
    sandbox: boolean
  }
  snapscan: {
    apiKey: string
    snapCode: string
    sandbox: boolean
  }
  ozow: {
    apiKey: string
    siteCode: string
    privateKey: string
    sandbox: boolean
  }
}

export interface PaymentRequest {
  amount: number
  currency: 'ZAR' | 'USD'
  gateway: 'payfast' | 'paypal' | 'snapscan' | 'ozow'
  purpose: string
  frequency: 'ONE_TIME' | 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY'
  donorEmail: string
  donorName: string
  returnUrl: string
  cancelUrl: string
  notifyUrl: string
  donationId: string
}

export interface PaymentResponse {
  success: boolean
  paymentUrl?: string
  paymentId?: string
  error?: string
  metadata?: Record<string, any>
}

export interface WebhookVerification {
  isValid: boolean
  data?: Record<string, any>
  error?: string
}

export class PaymentService {
  private config: PaymentConfig
  private payfast: PayFast | null = null
  private paypal: Client | null = null

  constructor() {
    this.config = {
      payfast: {
        merchantId: process.env.PAYFAST_MERCHANT_ID!,
        merchantKey: process.env.PAYFAST_MERCHANT_KEY!,
        passphrase: process.env.PAYFAST_PASSPHRASE!,
        sandbox: process.env.NODE_ENV !== 'production'
      },
      paypal: {
        clientId: process.env.PAYPAL_CLIENT_ID!,
        clientSecret: process.env.PAYPAL_CLIENT_SECRET!,
        sandbox: process.env.NODE_ENV !== 'production'
      },
      snapscan: {
        apiKey: process.env.SNAPSCAN_API_KEY!,
        snapCode: process.env.SNAPSCAN_SNAP_CODE!,
        sandbox: process.env.NODE_ENV !== 'production'
      }
    }

    this.initializeGateways()
  }

  private initializeGateways() {
    // Initialize PayFast
    if (this.config.payfast.merchantId && this.config.payfast.merchantKey) {
      this.payfast = new PayFast({
        sandbox: this.config.payfast.sandbox,
        merchant_id: this.config.payfast.merchantId,
        merchant_key: this.config.payfast.merchantKey,
        passphrase: this.config.payfast.passphrase
      })
    }

    // Initialize PayPal
    if (this.config.paypal.clientId && this.config.paypal.clientSecret) {
      this.paypal = new Client({
        clientCredentialsAuthCredentials: {
          oAuthClientId: this.config.paypal.clientId,
          oAuthClientSecret: this.config.paypal.clientSecret
        },
        environment: this.config.paypal.sandbox ? 'sandbox' : 'production'
      })
    }
  }

  async createPayment(request: PaymentRequest, ipAddress?: string, userAgent?: string): Promise<PaymentResponse> {
    try {
      // Security checks
      if (ipAddress && userAgent) {
        // Perform fraud detection
        const fraudCheck = await securityService.performFraudCheck(
          request.amount,
          request.currency,
          request.donorEmail,
          ipAddress,
          userAgent
        )

        if (fraudCheck.recommendation === 'BLOCK') {
          await securityService.logSecurityEvent({
            type: 'SUSPICIOUS_ACTIVITY',
            userId: request.donorEmail,
            ipAddress,
            userAgent,
            metadata: {
              reason: 'FRAUD_DETECTION_BLOCK',
              riskScore: fraudCheck.riskScore,
              flags: fraudCheck.flags
            },
            severity: 'HIGH'
          })

          return {
            success: false,
            error: 'Payment blocked for security reasons. Please contact support.'
          }
        }

        // Log payment attempt
        await securityService.logSecurityEvent({
          type: 'PAYMENT_ATTEMPT',
          userId: request.donorEmail,
          ipAddress,
          userAgent,
          metadata: {
            amount: request.amount,
            currency: request.currency,
            gateway: request.gateway,
            riskScore: fraudCheck.riskScore
          },
          severity: fraudCheck.recommendation === 'REVIEW' ? 'MEDIUM' : 'LOW'
        })
      }

      if (request.gateway === 'payfast' && request.currency === 'ZAR') {
        return await this.createPayFastPayment(request)
      } else if (request.gateway === 'snapscan' && request.currency === 'ZAR') {
        return await this.createSnapScanPayment(request)
      } else if (request.gateway === 'paypal') {
        return await this.createPayPalPayment(request)
      } else {
        throw new Error(`Unsupported gateway ${request.gateway} for currency ${request.currency}`)
      }
    } catch (error) {
      console.error('Payment creation error:', error)

      // Log payment failure
      if (ipAddress && userAgent) {
        await securityService.recordFailedAttempt(
          request.donorEmail,
          'PAYMENT_CREATION_ERROR',
          { ipAddress, userAgent, error: error instanceof Error ? error.message : 'Unknown error' }
        )
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown payment error'
      }
    }
  }

  private async createPayFastPayment(request: PaymentRequest): Promise<PaymentResponse> {
    if (!this.payfast) {
      throw new Error('PayFast not configured')
    }

    const paymentData = {
      return_url: request.returnUrl,
      cancel_url: request.cancelUrl,
      notify_url: request.notifyUrl,
      name_first: request.donorName.split(' ')[0] || 'Anonymous',
      name_last: request.donorName.split(' ').slice(1).join(' ') || 'Donor',
      email_address: request.donorEmail,
      m_payment_id: request.donationId,
      amount: request.amount.toFixed(2),
      item_name: `PROTEC Donation - ${request.purpose}`,
      item_description: `Donation to PROTEC Alumni Platform for ${request.purpose}`,
      email_confirmation: '1',
      confirmation_address: request.donorEmail,
      // Subscription fields for recurring donations
      ...(request.frequency !== 'ONE_TIME' && {
        subscription_type: '1',
        frequency: this.getPayFastFrequency(request.frequency),
        cycles: '0', // Indefinite
        subscription_notify_email: 'true',
        subscription_notify_buyer: 'true'
      })
    }

    try {
      const urlString = this.payfast.createStringfromObject(paymentData)
      const hash = this.payfast.createSignature(urlString)
      const paymentObject = this.payfast.createPaymentObject(paymentData, hash)
      const paymentUrl = await this.payfast.generatePaymentUrl(paymentObject)

      return {
        success: true,
        paymentUrl,
        paymentId: request.donationId,
        metadata: { gateway: 'payfast', paymentData }
      }
    } catch (error) {
      throw new Error(`PayFast payment creation failed: ${error}`)
    }
  }

  private async createSnapScanPayment(request: PaymentRequest): Promise<PaymentResponse> {
    if (!this.config.snapscan.apiKey || !this.config.snapscan.snapCode) {
      throw new Error('SnapScan not configured')
    }

    try {
      // SnapScan uses QR codes for payments
      // Generate a unique merchant reference for this payment
      const merchantReference = `PROTEC_${request.donationId}`

      // Convert amount to cents (SnapScan expects amount in cents)
      const amountInCents = Math.round(request.amount * 100)

      // Build the SnapScan payment URL
      const baseUrl = this.config.snapscan.sandbox
        ? 'https://pos.snapscan.io/qr'
        : 'https://pos.snapscan.io/qr'

      const paymentUrl = `${baseUrl}/${this.config.snapscan.snapCode}?id=${merchantReference}&amount=${amountInCents}&strict=true`

      // For recurring donations, we'll need to handle them differently
      // SnapScan doesn't have built-in recurring payments like PayFast
      if (request.frequency !== 'ONE_TIME') {
        console.warn('SnapScan does not support recurring payments. This will be a one-time payment.')
      }

      return {
        success: true,
        paymentUrl,
        paymentId: merchantReference,
        metadata: {
          gateway: 'snapscan',
          snapCode: this.config.snapscan.snapCode,
          merchantReference,
          amountInCents,
          qrCodeUrl: `${paymentUrl}&snap_code_size=200`
        }
      }
    } catch (error) {
      throw new Error(`SnapScan payment creation failed: ${error}`)
    }
  }

  private async createPayPalPayment(request: PaymentRequest): Promise<PaymentResponse> {
    if (!this.paypal) {
      throw new Error('PayPal not configured')
    }

    const ordersController = new OrdersController(this.paypal)

    const orderRequest = {
      intent: 'CAPTURE',
      purchase_units: [{
        reference_id: request.donationId,
        amount: {
          currency_code: request.currency,
          value: request.amount.toFixed(2)
        },
        description: `PROTEC Donation - ${request.purpose}`,
        custom_id: request.donationId
      }],
      application_context: {
        return_url: request.returnUrl,
        cancel_url: request.cancelUrl,
        brand_name: 'PROTEC Alumni Platform',
        landing_page: 'BILLING',
        user_action: 'PAY_NOW'
      }
    }

    try {
      const response = await ordersController.create({
        body: orderRequest,
        prefer: 'return=representation'
      })

      if (response.statusCode === 201 && response.result) {
        const order = response.result
        const approvalLink = order.links?.find(link => link.rel === 'approve')

        return {
          success: true,
          paymentUrl: approvalLink?.href,
          paymentId: order.id,
          metadata: { gateway: 'paypal', order }
        }
      } else {
        throw new Error('PayPal order creation failed')
      }
    } catch (error) {
      throw new Error(`PayPal payment creation failed: ${error}`)
    }
  }

  async verifyWebhook(gateway: 'payfast' | 'paypal' | 'snapscan', payload: any, headers: Record<string, string>): Promise<WebhookVerification> {
    try {
      if (gateway === 'payfast') {
        return await this.verifyPayFastWebhook(payload, headers)
      } else if (gateway === 'snapscan') {
        return await this.verifySnapScanWebhook(payload, headers)
      } else if (gateway === 'paypal') {
        return await this.verifyPayPalWebhook(payload, headers)
      } else {
        return { isValid: false, error: 'Unsupported gateway' }
      }
    } catch (error) {
      console.error('Webhook verification error:', error)
      return { 
        isValid: false, 
        error: error instanceof Error ? error.message : 'Webhook verification failed' 
      }
    }
  }

  private async verifyPayFastWebhook(payload: any, headers: Record<string, string>): Promise<WebhookVerification> {
    // PayFast webhook verification
    const pfParamString = Object.keys(payload)
      .filter(key => key !== 'signature')
      .sort()
      .map(key => `${key}=${encodeURIComponent(payload[key]).replace(/%20/g, '+')}`)
      .join('&')

    const pfParamStringWithPassphrase = `${pfParamString}&passphrase=${encodeURIComponent(this.config.payfast.passphrase)}`
    const signature = createHash('md5').update(pfParamStringWithPassphrase).digest('hex')

    const isValid = signature === payload.signature

    return {
      isValid,
      data: isValid ? payload : undefined,
      error: isValid ? undefined : 'Invalid PayFast signature'
    }
  }

  private async verifyPayPalWebhook(payload: any, headers: Record<string, string>): Promise<WebhookVerification> {
    // PayPal webhook verification would require webhook ID and certificate
    // For now, we'll implement basic verification
    const authAlgo = headers['paypal-auth-algo']
    const transmission = headers['paypal-transmission-id']
    const certId = headers['paypal-cert-id']
    const signature = headers['paypal-transmission-sig']
    const timestamp = headers['paypal-transmission-time']

    // Basic validation - in production, implement full certificate verification
    if (!authAlgo || !transmission || !certId || !signature || !timestamp) {
      return { isValid: false, error: 'Missing PayPal webhook headers' }
    }

    // For now, accept all PayPal webhooks with proper headers
    // TODO: Implement full certificate verification
    return {
      isValid: true,
      data: payload
    }
  }

  private async verifySnapScanWebhook(payload: any, headers: Record<string, string>): Promise<WebhookVerification> {
    try {
      // SnapScan webhook verification using HMAC signature
      const authHeader = headers['authorization'] || headers['Authorization']

      if (!authHeader || !authHeader.startsWith('SnapScan signature=')) {
        return { isValid: false, error: 'Missing or invalid authorization header' }
      }

      // Extract the signature from the header
      const receivedSignature = authHeader.replace('SnapScan signature=', '')

      // Get the webhook auth key from environment
      const webhookAuthKey = process.env.SNAPSCAN_WEBHOOK_AUTH_KEY
      if (!webhookAuthKey) {
        return { isValid: false, error: 'SnapScan webhook auth key not configured' }
      }

      // Create HMAC signature of the raw payload
      const expectedSignature = createHmac('sha256', webhookAuthKey)
        .update(JSON.stringify(payload))
        .digest('hex')

      // Compare signatures using constant-time comparison
      const isValid = createHmac('sha256', 'dummy')
        .update(receivedSignature)
        .digest('hex') === createHmac('sha256', 'dummy')
        .update(expectedSignature)
        .digest('hex')

      if (!isValid) {
        return { isValid: false, error: 'Invalid signature' }
      }

      return {
        isValid: true,
        data: payload
      }
    } catch (error) {
      console.error('SnapScan webhook verification error:', error)
      return {
        isValid: false,
        error: error instanceof Error ? error.message : 'Webhook verification failed'
      }
    }
  }

  private getPayFastFrequency(frequency: string): string {
    switch (frequency) {
      case 'MONTHLY': return '3'
      case 'QUARTERLY': return '4'
      case 'ANNUALLY': return '6'
      default: return '3' // Default to monthly
    }
  }

  async capturePayPalPayment(orderId: string): Promise<PaymentResponse> {
    if (!this.paypal) {
      throw new Error('PayPal not configured')
    }

    const ordersController = new OrdersController(this.paypal)

    try {
      const response = await ordersController.ordersCapture({
        id: orderId,
        prefer: 'return=representation'
      })

      if (response.statusCode === 201 && response.result) {
        return {
          success: true,
          paymentId: response.result.id,
          metadata: { gateway: 'paypal', capture: response.result }
        }
      } else {
        throw new Error('PayPal capture failed')
      }
    } catch (error) {
      throw new Error(`PayPal capture failed: ${error}`)
    }
  }

  async refundPayment(gateway: 'payfast' | 'paypal', paymentId: string, amount?: number): Promise<PaymentResponse> {
    try {
      if (gateway === 'paypal') {
        return await this.refundPayPalPayment(paymentId, amount)
      } else {
        // PayFast refunds are typically handled through their dashboard
        return {
          success: false,
          error: 'PayFast refunds must be processed through the PayFast dashboard'
        }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Refund failed'
      }
    }
  }

  private async refundPayPalPayment(captureId: string, amount?: number): Promise<PaymentResponse> {
    if (!this.paypal) {
      throw new Error('PayPal not configured')
    }

    const paymentsController = new PaymentsController(this.paypal)

    try {
      const refundRequest: any = {
        amount: amount ? {
          currency_code: 'USD', // This should be dynamic based on original payment
          value: amount.toFixed(2)
        } : undefined
      }

      const response = await paymentsController.capturesRefund({
        id: captureId,
        body: refundRequest,
        prefer: 'return=representation'
      })

      if (response.statusCode === 201 && response.result) {
        return {
          success: true,
          paymentId: response.result.id,
          metadata: { gateway: 'paypal', refund: response.result }
        }
      } else {
        throw new Error('PayPal refund failed')
      }
    } catch (error) {
      throw new Error(`PayPal refund failed: ${error}`)
    }
  }
}

// Singleton instance
export const paymentService = new PaymentService()
